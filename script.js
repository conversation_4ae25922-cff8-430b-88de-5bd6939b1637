// ===== 全局变量和状态管理 =====
class LearningRoadmapApp {
    constructor() {
      // 应用状态
      this.roadmapData = [];
      this.learningProgress = {};
      this.studyTime = {};
      this.currentTimer = null;
      this.timerInterval = null;
      this.currentStudyItem = null;
      this.sessionStartTime = null;
      
      // DOM 元素引用
      this.elements = {
        // 上传相关
        fileInput: document.getElementById('fileInput'),
        uploadArea: document.getElementById('uploadArea'),
        parseBtn: document.getElementById('parseBtn'),
        clearBtn: document.getElementById('clearBtn'),
        
        // 统计显示
        totalItems: document.getElementById('totalItems'),
        completedItems: document.getElementById('completedItems'),
        totalTime: document.getElementById('totalTime'),
        progressPercent: document.getElementById('progressPercent'),
        progressFill: document.getElementById('progressFill'),
        
        // 控制按钮
        darkModeToggle: document.getElementById('darkModeToggle'),
        collapseAllBtn: document.getElementById('collapseAllBtn'),
        expandAllBtn: document.getElementById('expandAllBtn'),
        resetProgressBtn: document.getElementById('resetProgressBtn'),
        
        // 内容展示
        roadmapContent: document.getElementById('roadmapContent'),
        
        // 时间追踪
        timeTracker: document.getElementById('timeTracker'),
        closeTracker: document.getElementById('closeTracker'),
        currentItem: document.getElementById('currentItem'),
        timerDisplay: document.getElementById('timerDisplay'),
        startTimer: document.getElementById('startTimer'),
        pauseTimer: document.getElementById('pauseTimer'),
        stopTimer: document.getElementById('stopTimer'),
        sessionNotes: document.getElementById('sessionNotes'),
        saveNotes: document.getElementById('saveNotes'),
        
        // 模态框
        modalOverlay: document.getElementById('modalOverlay')
      };
      
      this.init();
    }
    
    // ===== 初始化方法 =====
    init() {
      this.loadStoredData();
      this.bindEvents();
      this.updateStats();
      this.initTheme();
    }
    
    // ===== 事件绑定 =====
    bindEvents() {
      // 文件上传相关
      this.elements.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
      this.elements.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
      this.elements.uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
      this.elements.uploadArea.addEventListener('click', () => this.elements.fileInput.click());
      
      // 按钮事件
      this.elements.parseBtn.addEventListener('click', () => this.parseMarkdown());
      this.elements.clearBtn.addEventListener('click', () => this.clearContent());
      this.elements.darkModeToggle.addEventListener('click', () => this.toggleDarkMode());
      this.elements.collapseAllBtn.addEventListener('click', () => this.collapseAll());
      this.elements.expandAllBtn.addEventListener('click', () => this.expandAll());
      this.elements.resetProgressBtn.addEventListener('click', () => this.resetProgress());
      
      // 时间追踪相关
      this.elements.closeTracker.addEventListener('click', () => this.closeTimeTracker());
      this.elements.startTimer.addEventListener('click', () => this.startTimer());
      this.elements.pauseTimer.addEventListener('click', () => this.pauseTimer());
      this.elements.stopTimer.addEventListener('click', () => this.stopTimer());
      this.elements.saveNotes.addEventListener('click', () => this.saveSessionNotes());
      
      // 模态框关闭
      this.elements.modalOverlay.addEventListener('click', () => this.closeTimeTracker());
    }
    
    // ===== 文件处理方法 =====
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        this.readFile(file);
      }
    }
    
    handleDragOver(event) {
      event.preventDefault();
      this.elements.uploadArea.classList.add('dragover');
    }
    
    handleFileDrop(event) {
      event.preventDefault();
      this.elements.uploadArea.classList.remove('dragover');
      
      const files = event.dataTransfer.files;
      if (files.length > 0) {
        this.readFile(files[0]);
      }
    }
    
    readFile(file) {
      if (!file.name.match(/\.(md|txt)$/i)) {
        alert('请选择 Markdown (.md) 或文本 (.txt) 文件');
        return;
      }
      
      const reader = new FileReader();
      reader.onload = (e) => {
        this.rawMarkdown = e.target.result;
        this.elements.parseBtn.disabled = false;
        this.updateUploadDisplay(file.name);
      };
      reader.readAsText(file, 'UTF-8');
    }
    
    updateUploadDisplay(filename) {
      const uploadContent = this.elements.uploadArea.querySelector('.upload-content');
      uploadContent.innerHTML = `
        <div class="upload-icon">📄</div>
        <p class="upload-text">已选择文件: ${filename}</p>
        <button class="btn-secondary" onclick="document.getElementById('fileInput').click()">
          重新选择
        </button>
      `;
    }
    
    // ===== Markdown 解析方法 =====
    parseMarkdown() {
      if (!this.rawMarkdown) return;
      
      const lines = this.rawMarkdown.split('\n');
      this.roadmapData = [];
      
      let currentSection = null;
      let currentSubsection = null;
      let itemId = 0;
      
      lines.forEach(line => {
        const trimmedLine = line.trim();
        if (!trimmedLine) return;
        
        // 检测标题级别
        const headerMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/);
        if (headerMatch) {
          const level = headerMatch[1].length;
          const title = headerMatch[2];
          
          if (level === 1) {
            // 一级标题 - 主要章节
            currentSection = {
              id: ++itemId,
              type: 'section',
              level: 1,
              title: title,
              completed: false,
              children: [],
              collapsed: false
            };
            this.roadmapData.push(currentSection);
            currentSubsection = null;
          } else if (level === 2 && currentSection) {
            // 二级标题 - 子章节
            currentSubsection = {
              id: ++itemId,
              type: 'subsection',
              level: 2,
              title: title,
              completed: false,
              children: [],
              collapsed: false
            };
            currentSection.children.push(currentSubsection);
          } else if (level >= 3 && currentSubsection) {
            // 三级及以上标题 - 学习项目
            const item = {
              id: ++itemId,
              type: 'item',
              level: level,
              title: title,
              completed: false,
              timeSpent: 0,
              notes: ''
            };
            currentSubsection.children.push(item);
          } else if (level >= 3 && currentSection && !currentSubsection) {
            // 直接在主章节下的学习项目
            const item = {
              id: ++itemId,
              type: 'item',
              level: level,
              title: title,
              completed: false,
              timeSpent: 0,
              notes: ''
            };
            currentSection.children.push(item);
          }
        } else if (trimmedLine.startsWith('-') || trimmedLine.startsWith('*')) {
          // 列表项目
          const title = trimmedLine.replace(/^[-*]\s*/, '');
          const item = {
            id: ++itemId,
            type: 'item',
            level: 0,
            title: title,
            completed: false,
            timeSpent: 0,
            notes: ''
          };
          
          if (currentSubsection) {
            currentSubsection.children.push(item);
          } else if (currentSection) {
            currentSection.children.push(item);
          } else {
            this.roadmapData.push(item);
          }
        }
      });
      
      this.renderRoadmap();
      this.updateStats();
      this.saveData();
    }
    
    // ===== 渲染学习路线 =====
    renderRoadmap() {
      if (this.roadmapData.length === 0) {
        this.showEmptyState();
        return;
      }
      
      const html = this.roadmapData.map(item => this.renderItem(item)).join('');
      this.elements.roadmapContent.innerHTML = html;
      this.bindItemEvents();
    }
    
    renderItem(item, depth = 0) {
      const indent = depth * 20;
      const hasChildren = item.children && item.children.length > 0;
      
      if (item.type === 'section' || item.type === 'subsection') {
        return `
          <div class="roadmap-section-item" style="margin-left: ${indent}px">
            <div class="section-header ${item.completed ? 'completed' : ''}" data-id="${item.id}">
              <div class="section-content">
                ${hasChildren ? `<button class="collapse-btn ${item.collapsed ? 'collapsed' : ''}" data-id="${item.id}">
                  <span class="collapse-icon">${item.collapsed ? '▶' : '▼'}</span>
                </button>` : ''}
                <h${item.level + 2} class="section-title">${item.title}</h${item.level + 2}>
                <div class="section-controls">
                  <span class="progress-indicator">${this.getChildrenProgress(item)}</span>
                  <button class="btn-checkbox ${item.completed ? 'checked' : ''}" data-id="${item.id}" title="标记完成">
                    <span class="checkmark">✓</span>
                  </button>
                </div>
              </div>
            </div>
            <div class="section-children ${item.collapsed ? 'collapsed' : ''}">
              ${hasChildren ? item.children.map(child => this.renderItem(child, depth + 1)).join('') : ''}
            </div>
          </div>
        `;
      } else {
        return `
          <div class="roadmap-item ${item.completed ? 'completed' : ''}" style="margin-left: ${indent}px" data-id="${item.id}">
            <div class="item-content">
              <button class="btn-checkbox ${item.completed ? 'checked' : ''}" data-id="${item.id}" title="标记完成">
                <span class="checkmark">✓</span>
              </button>
              <span class="item-title">${item.title}</span>
              <div class="item-controls">
                <span class="time-spent">${this.formatTime(item.timeSpent || 0)}</span>
                <button class="btn-timer" data-id="${item.id}" title="开始计时">⏱️</button>
              </div>
            </div>
            ${item.notes ? `<div class="item-notes">${item.notes}</div>` : ''}
          </div>
        `;
      }
    }
    
    getChildrenProgress(item) {
      if (!item.children || item.children.length === 0) return '';
      
      const total = this.countAllItems(item);
      const completed = this.countCompletedItems(item);
      return `${completed}/${total}`;
    }
    
    countAllItems(item) {
      let count = 0;
      if (item.type === 'item') count = 1;
      
      if (item.children) {
        item.children.forEach(child => {
          count += this.countAllItems(child);
        });
      }
      
      return count;
    }
    
    countCompletedItems(item) {
      let count = 0;
      if (item.type === 'item' && item.completed) count = 1;
      
      if (item.children) {
        item.children.forEach(child => {
          count += this.countCompletedItems(child);
        });
      }
      
      return count;
    }
    
    // ===== 事件绑定（渲染后） =====
    bindItemEvents() {
      // 复选框事件
      document.querySelectorAll('.btn-checkbox').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.stopPropagation();
          const id = parseInt(btn.dataset.id);
          this.toggleItemCompletion(id);
        });
      });
      
      // 折叠按钮事件
      document.querySelectorAll('.collapse-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.stopPropagation();
          const id = parseInt(btn.dataset.id);
          this.toggleItemCollapse(id);
        });
      });
      
      // 计时器按钮事件
      document.querySelectorAll('.btn-timer').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.stopPropagation();
          const id = parseInt(btn.dataset.id);
          this.startStudySession(id);
        });
      });
    }
    
    // ===== 项目状态切换 =====
    toggleItemCompletion(id) {
      const item = this.findItemById(id);
      if (item) {
        item.completed = !item.completed;
        this.renderRoadmap();
        this.updateStats();
        this.saveData();
      }
    }
    
    toggleItemCollapse(id) {
      const item = this.findItemById(id);
      if (item) {
        item.collapsed = !item.collapsed;
        this.renderRoadmap();
        this.saveData();
      }
    }
    
    // ===== 工具方法 =====
    findItemById(id, items = this.roadmapData) {
      for (let item of items) {
        if (item.id === id) return item;
        if (item.children) {
          const found = this.findItemById(id, item.children);
          if (found) return found;
        }
      }
      return null;
    }
    
    // ===== 统计更新 =====
    updateStats() {
      const total = this.countAllItems({ children: this.roadmapData });
      const completed = this.countCompletedItems({ children: this.roadmapData });
      const totalTime = this.calculateTotalTime();
      const progress = total > 0 ? Math.round((completed / total) * 100) : 0;
      
      this.elements.totalItems.textContent = total;
      this.elements.completedItems.textContent = completed;
      this.elements.totalTime.textContent = this.formatTime(totalTime);
      this.elements.progressPercent.textContent = `${progress}%`;
      this.elements.progressFill.style.width = `${progress}%`;
    }
    
    calculateTotalTime() {
      let total = 0;
      const addTime = (item) => {
        if (item.timeSpent) total += item.timeSpent;
        if (item.children) item.children.forEach(addTime);
      };
      this.roadmapData.forEach(addTime);
      return total;
    }
    
    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      
      if (hours > 0) {
        return `${hours}h ${minutes}m`;
      } else if (minutes > 0) {
        return `${minutes}m ${secs}s`;
      } else {
        return `${secs}s`;
      }
    }
    
    formatTimerDisplay(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    
    // ===== 继续下一部分 =====
    
    // ===== 时间追踪功能 =====
    startStudySession(itemId) {
      const item = this.findItemById(itemId);
      if (!item) return;
      
      this.currentStudyItem = item;
      this.elements.currentItem.textContent = item.title;
      this.showTimeTracker();
      
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
      }
      
      this.currentTimer = 0;
      this.sessionStartTime = Date.now();
      this.updateTimerDisplay();
    }
    
    showTimeTracker() {
      this.elements.timeTracker.classList.add('active');
      this.elements.modalOverlay.classList.add('active');
    }
    
    closeTimeTracker() {
      this.elements.timeTracker.classList.remove('active');
      this.elements.modalOverlay.classList.remove('active');
      
      if (this.timerInterval) {
        this.stopTimer();
      }
    }
    
    startTimer() {
      if (this.timerInterval) return;
      
      this.sessionStartTime = Date.now() - (this.currentTimer * 1000);
      this.timerInterval = setInterval(() => {
        this.currentTimer = Math.floor((Date.now() - this.sessionStartTime) / 1000);
        this.updateTimerDisplay();
      }, 1000);
      
      this.elements.startTimer.disabled = true;
      this.elements.pauseTimer.disabled = false;
      this.elements.stopTimer.disabled = false;
    }
    
    pauseTimer() {
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
      }
      
      this.elements.startTimer.disabled = false;
      this.elements.pauseTimer.disabled = true;
    }
    
    stopTimer() {
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
      }
      
      if (this.currentStudyItem && this.currentTimer > 0) {
        this.currentStudyItem.timeSpent = (this.currentStudyItem.timeSpent || 0) + this.currentTimer;
        this.renderRoadmap();
        this.updateStats();
        this.saveData();
      }
      
      this.currentTimer = 0;
      this.currentStudyItem = null;
      this.updateTimerDisplay();
      
      this.elements.startTimer.disabled = false;
      this.elements.pauseTimer.disabled = true;
      this.elements.stopTimer.disabled = true;
    }
    
    updateTimerDisplay() {
      this.elements.timerDisplay.textContent = this.formatTimerDisplay(this.currentTimer);
    }
    
    saveSessionNotes() {
      if (this.currentStudyItem) {
        const notes = this.elements.sessionNotes.value.trim();
        if (notes) {
          this.currentStudyItem.notes = notes;
          this.saveData();
          this.elements.sessionNotes.value = '';
          alert('笔记已保存！');
        }
      }
    }
    
    // ===== 主题切换 =====
    initTheme() {
      const savedTheme = localStorage.getItem('theme') || 'light';
      document.documentElement.setAttribute('data-theme', savedTheme);
      this.updateThemeIcon(savedTheme);
    }
    
    toggleDarkMode() {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      
      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);
      this.updateThemeIcon(newTheme);
    }
    
    updateThemeIcon(theme) {
      const icon = this.elements.darkModeToggle.querySelector('.icon');
      icon.textContent = theme === 'dark' ? '☀️' : '🌙';
    }
    
    // ===== 折叠/展开控制 =====
    collapseAll() {
      this.setAllItemsCollapsed(true);
      this.renderRoadmap();
      this.saveData();
    }
    
    expandAll() {
      this.setAllItemsCollapsed(false);
      this.renderRoadmap();
      this.saveData();
    }
    
    setAllItemsCollapsed(collapsed, items = this.roadmapData) {
      items.forEach(item => {
        if (item.type === 'section' || item.type === 'subsection') {
          item.collapsed = collapsed;
        }
        if (item.children) {
          this.setAllItemsCollapsed(collapsed, item.children);
        }
      });
    }
    
    // ===== 数据重置 =====
    resetProgress() {
      if (confirm('确定要重置所有学习进度吗？此操作不可撤销！')) {
        this.resetAllProgress();
        this.renderRoadmap();
        this.updateStats();
        this.saveData();
      }
    }
    
    resetAllProgress(items = this.roadmapData) {
      items.forEach(item => {
        item.completed = false;
        item.timeSpent = 0;
        item.notes = '';
        if (item.children) {
          this.resetAllProgress(item.children);
        }
      });
    }
    
    // ===== 清空内容 =====
    clearContent() {
      if (confirm('确定要清空所有内容吗？')) {
        this.roadmapData = [];
        this.rawMarkdown = '';
        this.elements.parseBtn.disabled = true;
        this.showEmptyState();
        this.updateStats();
        this.resetUploadArea();
        this.clearStoredData();
      }
    }
    
    resetUploadArea() {
      const uploadContent = this.elements.uploadArea.querySelector('.upload-content');
      uploadContent.innerHTML = `
        <div class="upload-icon">📁</div>
        <p class="upload-text">拖拽文件到这里或点击选择</p>
        <input type="file" id="fileInput" accept=".md,.txt" hidden>
        <button class="btn-primary" onclick="document.getElementById('fileInput').click()">
          选择文件
        </button>
      `;
    }
    
    showEmptyState() {
      this.elements.roadmapContent.innerHTML = `
        <div class="empty-state">
          <div class="empty-icon">📋</div>
          <h3>暂无学习路线</h3>
          <p>请上传 Markdown 文件来生成您的学习路线</p>
        </div>
      `;
    }
    
    // ===== 数据持久化 =====
    saveData() {
      const data = {
        roadmapData: this.roadmapData,
        timestamp: Date.now()
      };
      localStorage.setItem('learningRoadmapData', JSON.stringify(data));
    }
    
    loadStoredData() {
      const stored = localStorage.getItem('learningRoadmapData');
      if (stored) {
        try {
          const data = JSON.parse(stored);
          this.roadmapData = data.roadmapData || [];
          if (this.roadmapData.length > 0) {
            this.renderRoadmap();
          }
        } catch (e) {
          console.error('加载存储数据失败:', e);
        }
      }
    }
    
    clearStoredData() {
      localStorage.removeItem('learningRoadmapData');
    }
  }
  
  // ===== 应用初始化 =====
  document.addEventListener('DOMContentLoaded', () => {
    window.app = new LearningRoadmapApp();
  });
  
  // ===== 路线项目样式（需要添加到CSS中） =====
  const additionalCSS = `
  /* 学习路线项目样式 */
  .roadmap-section-item {
    margin-bottom: var(--spacing-sm);
  }
  
  .section-header {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
  }
  
  .section-header:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
  }
  
  .section-header.completed {
    background: var(--primary-light);
    border-color: var(--primary-color);
  }
  
  .section-content {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
  }
  
  .collapse-btn {
    background: none;
    border: none;
    font-size: 1rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: var(--transition);
    color: var(--text-secondary);
  }
  
  .collapse-btn:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
  }
  
  .collapse-icon {
    display: inline-block;
    transition: transform 0.2s ease;
  }
  
  .collapse-btn.collapsed .collapse-icon {
    transform: rotate(-90deg);
  }
  
  .section-title {
    flex: 1;
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
  }
  
  .section-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }
  
  .progress-indicator {
    font-size: 0.875rem;
    color: var(--text-secondary);
    background: var(--bg-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    border: 1px solid var(--border-color);
  }
  
  .section-children {
    padding-left: var(--spacing-md);
    overflow: hidden;
    transition: max-height 0.3s ease;
  }
  
  .section-children.collapsed {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  
  .roadmap-item {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-sm);
    transition: var(--transition);
  }
  
  .roadmap-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
  }
  
  .roadmap-item.completed {
    background: var(--primary-light);
    border-color: var(--primary-color);
  }
  
  .item-content {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
  }
  
  .item-title {
    flex: 1;
    font-weight: 500;
  }
  
  .roadmap-item.completed .item-title {
    text-decoration: line-through;
    opacity: 0.7;
  }
  
  .item-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }
  
  .time-spent {
    font-size: 0.875rem;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
  }
  
  .btn-timer {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: var(--transition);
  }
  
  .btn-timer:hover {
    background: var(--bg-secondary);
  }
  
  .btn-checkbox {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    flex-shrink: 0;
  }
  
  .btn-checkbox:hover {
    border-color: var(--primary-color);
  }
  
  .btn-checkbox.checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .checkmark {
    color: white;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }
  
  .btn-checkbox.checked .checkmark {
    opacity: 1;
  }
  
  .item-notes {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    font-size: 0.875rem;
    color: var(--text-secondary);
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
  }
  `;
  
  // 动态添加CSS样式
  const styleSheet = document.createElement('style');
  styleSheet.textContent = additionalCSS;
  document.head.appendChild(styleSheet);