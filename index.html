<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习路线管理系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-content">
                <h1 class="logo">📚 学习路线管理</h1>
                <div class="header-controls">
                    <button id="darkModeToggle" class="btn-icon" title="切换深色模式">
                        <span class="icon">🌙</span>
                    </button>
                    <button id="collapseAllBtn" class="btn-secondary">折叠全部</button>
                    <button id="expandAllBtn" class="btn-secondary">展开全部</button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 上传区域 -->
            <section class="upload-section">
                <div class="upload-card">
                    <div class="upload-header">
                        <h2>📄 上传学习路线</h2>
                        <p>支持 Markdown 格式的学习路线文档</p>
                    </div>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <div class="upload-icon">📁</div>
                            <p class="upload-text">拖拽文件到这里或点击选择</p>
                            <input type="file" id="fileInput" accept=".md,.txt" hidden>
                            <button class="btn-primary" onclick="document.getElementById('fileInput').click()">
                                选择文件
                            </button>
                        </div>
                    </div>
                    <div class="upload-actions">
                        <button id="parseBtn" class="btn-primary" disabled>解析并生成路线</button>
                        <button id="clearBtn" class="btn-secondary">清空内容</button>
                    </div>
                </div>
            </section>

            <!-- 统计信息区域 -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="totalItems">0</h3>
                            <p class="stat-label">总学习项目</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="completedItems">0</h3>
                            <p class="stat-label">已完成</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⏱️</div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="totalTime">0h</h3>
                            <p class="stat-label">总学习时间</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🎯</div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="progressPercent">0%</h3>
                            <p class="stat-label">完成进度</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 学习路线展示区域 -->
            <section class="roadmap-section">
                <div class="roadmap-header">
                    <h2>🗺️ 学习路线</h2>
                    <div class="roadmap-controls">
                        <div class="progress-bar-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                        </div>
                        <button id="resetProgressBtn" class="btn-secondary">重置进度</button>
                    </div>
                </div>
                
                <!-- 路线内容容器 -->
                <div class="roadmap-content" id="roadmapContent">
                    <div class="empty-state">
                        <div class="empty-icon">📋</div>
                        <h3>暂无学习路线</h3>
                        <p>请上传 Markdown 文件来生成您的学习路线</p>
                    </div>
                </div>
            </section>
        </main>

        <!-- 学习时间追踪面板 -->
        <aside class="time-tracker" id="timeTracker">
            <div class="tracker-header">
                <h3>⏰ 学习时间追踪</h3>
                <button class="btn-icon" id="closeTracker">✕</button>
            </div>
            <div class="tracker-content">
                <div class="current-session">
                    <h4>当前学习项目</h4>
                    <p id="currentItem">未选择</p>
                </div>
                <div class="timer-display">
                    <div class="timer" id="timerDisplay">00:00:00</div>
                    <div class="timer-controls">
                        <button id="startTimer" class="btn-primary">开始</button>
                        <button id="pauseTimer" class="btn-secondary" disabled>暂停</button>
                        <button id="stopTimer" class="btn-secondary" disabled>停止</button>
                    </div>
                </div>
                <div class="session-notes">
                    <textarea id="sessionNotes" placeholder="记录学习笔记..."></textarea>
                    <button id="saveNotes" class="btn-primary">保存笔记</button>
                </div>
            </div>
        </aside>

        <!-- 模态框遮罩 -->
        <div class="modal-overlay" id="modalOverlay"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>