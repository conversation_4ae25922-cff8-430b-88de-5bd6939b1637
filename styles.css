/* ===== 基础样式和CSS变量 ===== */
:root {
  /* 主色调 */
  --primary-color: #3B82F6;
  --primary-hover: #2563EB;
  --primary-light: #DBEAFE;
  
  /* 背景颜色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F3F4F6;
  --bg-card: #FFFFFF;
  
  /* 文字颜色 */
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --text-muted: #9CA3AF;
  
  /* 边框和分割线 */
  --border-color: #E5E7EB;
  --border-hover: #D1D5DB;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* 圆角 */
  --border-radius: 8px;
  --border-radius-lg: 12px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 动画 */
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 深色模式变量 */
[data-theme="dark"] {
  --bg-primary: #111827;
  --bg-secondary: #1F2937;
  --bg-card: #1F2937;
  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --text-muted: #9CA3AF;
  --border-color: #374151;
  --border-hover: #4B5563;
}

/* ===== 全局重置和基础样式 ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: var(--transition);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

/* ===== 顶部导航栏 ===== */
.header {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
}

.logo {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.header-controls {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

/* ===== 按钮样式 ===== */
.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
}

.btn-secondary:hover {
  background: var(--border-color);
  border-color: var(--border-hover);
  transform: translateY(-1px);
}

.btn-icon {
  background: none;
  border: none;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-secondary);
  font-size: 1.2rem;
}

.btn-icon:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* ===== 主要内容区域 ===== */
.main-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* ===== 上传区域样式 ===== */
.upload-section {
  width: 100%;
}

.upload-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.upload-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.upload-header h2 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.upload-header p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.upload-area {
  padding: var(--spacing-xl);
  border: 2px dashed var(--border-color);
  margin: var(--spacing-md);
  border-radius: var(--border-radius);
  text-align: center;
  transition: var(--transition);
  cursor: pointer;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.upload-area.dragover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.upload-icon {
  font-size: 3rem;
  opacity: 0.6;
}

.upload-text {
  color: var(--text-secondary);
  font-size: 1rem;
}

.upload-actions {
  padding: var(--spacing-md);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  border-top: 1px solid var(--border-color);
}

/* ===== 统计信息区域 ===== */
.stats-section {
  width: 100%;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.stat-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

/* ===== 学习路线区域 ===== */
.roadmap-section {
  width: 100%;
}

.roadmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.roadmap-header h2 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin: 0;
}

.roadmap-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.progress-bar-container {
  min-width: 200px;
}

.progress-bar {
  height: 8px;
  background: var(--bg-secondary);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
  width: 0%;
  transition: width 0.3s ease;
}

.roadmap-content {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  min-height: 400px;
}

/* ===== 空状态样式 ===== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  height: 400px;
}

.empty-icon {
  font-size: 4rem;
  opacity: 0.4;
  margin-bottom: var(--spacing-md);
}

.empty-state h3 {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.empty-state p {
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* ===== 时间追踪面板 ===== */
.time-tracker {
  position: fixed;
  right: -400px;
  top: 50%;
  transform: translateY(-50%);
  width: 350px;
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  transition: right 0.3s ease;
  z-index: 1000;
}

.time-tracker.active {
  right: var(--spacing-md);
}

.tracker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.tracker-header h3 {
  font-size: 1.125rem;
  margin: 0;
}

.tracker-content {
  padding: var(--spacing-md);
}

.current-session {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.current-session h4 {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.current-session p {
  font-weight: 500;
  margin: 0;
}

.timer-display {
  text-align: center;
  margin-bottom: var(--spacing-md);
}

.timer {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  font-family: 'Courier New', monospace;
  margin-bottom: var(--spacing-md);
}

.timer-controls {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
}

.timer-controls button {
  flex: 1;
  max-width: 80px;
}

.session-notes {
  margin-top: var(--spacing-md);
}

.session-notes textarea {
  width: 100%;
  height: 100px;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
  resize: vertical;
  margin-bottom: var(--spacing-sm);
}

.session-notes textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

/* ===== 模态框遮罩 ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  z-index: 999;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-sm);
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .header-controls {
    justify-content: center;
  }
  
  .roadmap-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .roadmap-controls {
    flex-direction: column;
  }
  
  .progress-bar-container {
    min-width: auto;
  }
  
  .time-tracker {
    width: calc(100vw - 2 * var(--spacing-md));
    right: calc(-100vw + var(--spacing-md));
  }
  
  .time-tracker.active {
    right: var(--spacing-md);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .upload-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .timer {
    font-size: 2rem;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    font-size: 1.5rem;
  }
}